# -*- coding: utf-8 -*-
"""
Enhanced Dialog Systems Module

Centralized user input dialogs for depth range selection with Excel integration.
Handles parameter selection, depth ranges, and workflow decisions.

This module provides sophisticated Excel depth processing functionality
based on the reference implementation.
"""

import tkinter as tk
from tkinter import filedialog, simpledialog, ttk, messagebox
import numpy as np
import logging
from typing import Dict, List, Optional, Tuple, Any
import pandas as pd

# Import helper functions
import data_io

logger = logging.getLogger(__name__)


class EnhancedDepthDialog:
    """
    Enhanced dialog management for depth range selection with Excel integration.

    This class manages depth range selection including:
    - Manual depth input with validation
    - Excel file integration with boundary selection
    - Batch processing for multiple wells
    - Comprehensive error handling and validation
    """

    def __init__(self):
        """Initialize dialog systems with state management."""
        self.last_selections = {}  # Cache user selections for consistency
        self.state = {}  # Shared state for dialog coordination

    def select_boundaries_from_excel(self, df: pd.DataFrame, well_name: str) -> Optional[Tuple[float, float]]:
        """
        Create a dialog to select top and bottom boundaries from Excel data for a specific well.
        The dialog will only show boundaries for the specified well.

        Args:
            df: DataFrame containing boundary data
            well_name: Name of the well to filter data for

        Returns:
            Tuple of (top_depth, bottom_depth) or None if cancelled
        """
        # Filter data for the current well
        well_data = df[df['Well'] == well_name]

        if well_data.empty:
            messagebox.showerror(
                "Missing Well Data",
                f"No boundary data found for well '{well_name}' in the Excel file."
            )
            return None

        # Sort the data by depth to make selection more intuitive
        well_data = well_data.sort_values('MD')

        # Create dialog
        dialog = tk.Toplevel()
        dialog.title(f"Select Boundaries for {well_name}")
        dialog.geometry("400x300")

        # Create frame
        frame = ttk.Frame(dialog, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)

        # Get unique surface names for this well
        surfaces = well_data['Surface'].unique().tolist()

        # Create variables to store selections
        top_surface_var = tk.StringVar()
        bottom_surface_var = tk.StringVar()

        # Set default values if available
        if len(surfaces) > 0:
            top_surface_var.set(surfaces[0])
        if len(surfaces) > 1:
            bottom_surface_var.set(surfaces[-1])

        # Function to update depth labels when surface selection changes
        def update_depth_labels(*args):
            top_surface = top_surface_var.get()
            bottom_surface = bottom_surface_var.get()

            top_md = well_data[well_data['Surface'] == top_surface]['MD'].values
            bottom_md = well_data[well_data['Surface'] == bottom_surface]['MD'].values

            if len(top_md) > 0:
                top_depth_label.config(text=f"Depth: {top_md[0]:.2f}")
            else:
                top_depth_label.config(text="Depth: N/A")

            if len(bottom_md) > 0:
                bottom_depth_label.config(text=f"Depth: {bottom_md[0]:.2f}")
            else:
                bottom_depth_label.config(text="Depth: N/A")

        # Create widgets
        ttk.Label(frame, text="Select Top Boundary:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        top_combo = ttk.Combobox(frame, textvariable=top_surface_var, values=surfaces, state="readonly")
        top_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        top_depth_label = ttk.Label(frame, text="Depth: ")
        top_depth_label.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

        ttk.Label(frame, text="Select Bottom Boundary:").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        bottom_combo = ttk.Combobox(frame, textvariable=bottom_surface_var, values=surfaces, state="readonly")
        bottom_combo.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        bottom_depth_label = ttk.Label(frame, text="Depth: ")
        bottom_depth_label.grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=(0, 20))

        # Bind events to update depth labels
        top_surface_var.trace('w', update_depth_labels)
        bottom_surface_var.trace('w', update_depth_labels)

        # Initialize depth labels
        update_depth_labels()

        # Result variable
        result = {"boundaries": None}

        def on_ok():
            top_surface = top_surface_var.get()
            bottom_surface = bottom_surface_var.get()

            if not top_surface or not bottom_surface:
                messagebox.showerror("Selection Required", "Please select both top and bottom boundaries.")
                return

            # Get the depths
            top_md = well_data[well_data['Surface'] == top_surface]['MD'].values
            bottom_md = well_data[well_data['Surface'] == bottom_surface]['MD'].values

            if len(top_md) == 0 or len(bottom_md) == 0:
                messagebox.showerror("Invalid Selection", "Could not find depth values for selected boundaries.")
                return

            top_depth = float(top_md[0])
            bottom_depth = float(bottom_md[0])

            # Validate that top < bottom
            if top_depth >= bottom_depth:
                messagebox.showerror("Invalid Range", 
                    f"Top depth ({top_depth:.2f}) must be less than bottom depth ({bottom_depth:.2f}).")
                return

            result["boundaries"] = (top_depth, bottom_depth)
            dialog.destroy()

        def on_cancel():
            result["boundaries"] = None
            dialog.destroy()

        # Create buttons
        button_frame = ttk.Frame(frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=(10, 0))

        ttk.Button(button_frame, text="OK", command=on_ok).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Cancel", command=on_cancel).pack(side=tk.LEFT)

        # Configure grid weights
        frame.columnconfigure(1, weight=1)

        # Make dialog modal
        dialog.transient()
        dialog.grab_set()

        # Wait for dialog to close
        dialog.wait_window()

        return result["boundaries"]

    def _select_boundaries_for_all_wells(self, df: pd.DataFrame, las_well_names: List[str]) -> Optional[Dict[str, Tuple[float, float]]]:
        """
        Offers a comprehensive UI for setting boundaries for all loaded LAS wells at once,
        using the data from the Excel DataFrame.

        Args:
            df: DataFrame containing boundary data
            las_well_names: List of well names from LAS files

        Returns:
            Dictionary mapping well names to (top_depth, bottom_depth) tuples
        """
        # Filter data for wells that exist in both Excel and LAS files
        available_wells = [well for well in las_well_names if well in df['Well'].values]

        if not available_wells:
            messagebox.showerror(
                "No Matching Wells",
                "No wells from your LAS files were found in the Excel boundary data."
            )
            return None

        # Create dialog
        dialog = tk.Toplevel()
        dialog.title("Select Boundaries for All Wells")
        dialog.geometry("600x500")

        # Create main frame
        main_frame = ttk.Frame(dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Instructions
        instructions = ttk.Label(
            main_frame,
            text="Select top and bottom boundaries for each well from the Excel data:",
            font=("", 10, "bold")
        )
        instructions.pack(anchor=tk.W, pady=(0, 10))

        # Create scrollable frame for wells
        canvas = tk.Canvas(main_frame)
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Headers
        ttk.Label(scrollable_frame, text="Well", font=("", 9, "bold")).grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        ttk.Label(scrollable_frame, text="Top Surface", font=("", 9, "bold")).grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        ttk.Label(scrollable_frame, text="Top Depth", font=("", 9, "bold")).grid(row=0, column=2, padx=5, pady=5, sticky=tk.W)
        ttk.Label(scrollable_frame, text="Bottom Surface", font=("", 9, "bold")).grid(row=0, column=3, padx=5, pady=5, sticky=tk.W)
        ttk.Label(scrollable_frame, text="Bottom Depth", font=("", 9, "bold")).grid(row=0, column=4, padx=5, pady=5, sticky=tk.W)

        # Store selection variables
        well_selections = {}

        # Create selection widgets for each well
        for i, well_name in enumerate(available_wells, start=1):
            well_data = df[df['Well'] == well_name].sort_values('MD')
            surfaces = well_data['Surface'].unique().tolist()

            # Well name label
            ttk.Label(scrollable_frame, text=well_name).grid(row=i, column=0, padx=5, pady=2, sticky=tk.W)

            # Top surface selection
            top_var = tk.StringVar()
            if surfaces:
                top_var.set(surfaces[0])
            top_combo = ttk.Combobox(scrollable_frame, textvariable=top_var, values=surfaces, state="readonly", width=15)
            top_combo.grid(row=i, column=1, padx=5, pady=2)

            # Top depth label
            top_depth_label = ttk.Label(scrollable_frame, text="", width=10)
            top_depth_label.grid(row=i, column=2, padx=5, pady=2)

            # Bottom surface selection
            bottom_var = tk.StringVar()
            if len(surfaces) > 1:
                bottom_var.set(surfaces[-1])
            elif surfaces:
                bottom_var.set(surfaces[0])
            bottom_combo = ttk.Combobox(scrollable_frame, textvariable=bottom_var, values=surfaces, state="readonly", width=15)
            bottom_combo.grid(row=i, column=3, padx=5, pady=2)

            # Bottom depth label
            bottom_depth_label = ttk.Label(scrollable_frame, text="", width=10)
            bottom_depth_label.grid(row=i, column=4, padx=5, pady=2)

            # Store references
            well_selections[well_name] = {
                'top_var': top_var,
                'bottom_var': bottom_var,
                'top_label': top_depth_label,
                'bottom_label': bottom_depth_label,
                'well_data': well_data
            }

            # Function to update depth labels for this well
            def make_update_function(wname):
                def update_depths(*args):
                    selection = well_selections[wname]
                    top_surface = selection['top_var'].get()
                    bottom_surface = selection['bottom_var'].get()
                    data = selection['well_data']

                    # Update top depth
                    top_md = data[data['Surface'] == top_surface]['MD'].values
                    if len(top_md) > 0:
                        selection['top_label'].config(text=f"{top_md[0]:.2f}")
                    else:
                        selection['top_label'].config(text="N/A")

                    # Update bottom depth
                    bottom_md = data[data['Surface'] == bottom_surface]['MD'].values
                    if len(bottom_md) > 0:
                        selection['bottom_label'].config(text=f"{bottom_md[0]:.2f}")
                    else:
                        selection['bottom_label'].config(text="N/A")

                return update_depths

            # Bind update functions
            update_func = make_update_function(well_name)
            top_var.trace('w', update_func)
            bottom_var.trace('w', update_func)

            # Initialize depth labels
            update_func()

        # Result variable
        result = {"boundaries": None}

        # Button frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        def on_ok():
            boundaries = {}
            errors = []

            for well_name, selection in well_selections.items():
                top_surface = selection['top_var'].get()
                bottom_surface = selection['bottom_var'].get()
                data = selection['well_data']

                if not top_surface or not bottom_surface:
                    errors.append(f"Well '{well_name}': Please select both top and bottom surfaces")
                    continue

                # Get depths
                top_md = data[data['Surface'] == top_surface]['MD'].values
                bottom_md = data[data['Surface'] == bottom_surface]['MD'].values

                if len(top_md) == 0 or len(bottom_md) == 0:
                    errors.append(f"Well '{well_name}': Could not find depth values for selected surfaces")
                    continue

                top_depth = float(top_md[0])
                bottom_depth = float(bottom_md[0])

                # Validate range
                if top_depth >= bottom_depth:
                    errors.append(f"Well '{well_name}': Top depth ({top_depth:.2f}) must be less than bottom depth ({bottom_depth:.2f})")
                    continue

                boundaries[well_name] = (top_depth, bottom_depth)

            if errors:
                messagebox.showerror("Validation Errors", "\n".join(errors))
                return

            if not boundaries:
                messagebox.showerror("No Valid Selections", "No valid boundary selections were made.")
                return

            result["boundaries"] = boundaries
            dialog.destroy()

        def on_cancel():
            result["boundaries"] = None
            dialog.destroy()

        ttk.Button(button_frame, text="OK", command=on_ok).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Cancel", command=on_cancel).pack(side=tk.LEFT)

        # Make dialog modal
        dialog.transient()
        dialog.grab_set()

        # Wait for dialog to close
        dialog.wait_window()

        return result["boundaries"]

    def get_depth_ranges(self, las_files: List, log_keywords_for_finding_cols: Dict, preloaded_excel_df: Optional[pd.DataFrame] = None) -> Dict[str, Tuple[float, float]]:
        """
        Create a GUI to display well names and allow users to input top and bottom depths for analysis.
        Provides two methods:
        1. Manual input: Directly enter depth values
        2. Excel file import: Select depths from geological markers in an Excel file

        Args:
            las_files: List of LAS file objects
            log_keywords_for_finding_cols: Dictionary mapping generic names to possible mnemonics
            preloaded_excel_df: Optional pre-loaded Excel DataFrame with depth ranges

        Returns:
            Dictionary mapping well names to (top_depth, bottom_depth) tuples
        """
        # Calculate default depth values for each well
        default_depth_values_for_fallback = {}

        for las in las_files:
            well_name = las.well.WELL.value

            current_well_cols_for_depth = data_io.find_default_columns(las, log_keywords_for_finding_cols)
            depth_mnemonic = current_well_cols_for_depth.get('DEPTH')
            dt_mnemonic = current_well_cols_for_depth.get('DT')

            default_top_depth_val = 0.0  # Fallback default
            default_bottom_depth_val = 0.0 # Fallback default

            if depth_mnemonic and depth_mnemonic in las.curves:
                depth_data_for_range = np.array(las[depth_mnemonic].data)

                if dt_mnemonic and dt_mnemonic in las.curves:
                    dt_data_for_range = np.array(las[dt_mnemonic].data)
                    # Consider only depths where DT is valid
                    valid_dt_mask = np.isfinite(dt_data_for_range) & np.isfinite(depth_data_for_range)
                    valid_depths_for_dt_range = depth_data_for_range[valid_dt_mask]

                    if valid_depths_for_dt_range.size > 0:
                        default_top_depth_val = np.min(valid_depths_for_dt_range)
                        default_bottom_depth_val = np.max(valid_depths_for_dt_range)
                    elif np.any(np.isfinite(depth_data_for_range)): # Fallback to full depth range if DT is all NaN
                        default_top_depth_val = np.nanmin(depth_data_for_range)
                        default_bottom_depth_val = np.nanmax(depth_data_for_range)
                        logger.info(f"DT log '{dt_mnemonic}' for well {well_name} has no valid data points within depth range. Using full depth range for defaults.")
                    else: # Depth log itself is all NaN or empty
                        logger.warning(f"Depth log '{depth_mnemonic}' for well {well_name} is empty or all NaN. Using 0,0 for default depth range.")
                elif np.any(np.isfinite(depth_data_for_range)): # DT log not found, use full depth range
                    default_top_depth_val = np.nanmin(depth_data_for_range)
                    default_bottom_depth_val = np.nanmax(depth_data_for_range)
                    logger.info(f"DT log not found for well {well_name} (searched for '{dt_mnemonic}'). Using full depth range for defaults.")
                else: # Depth log is all NaN or empty, and DT log not found
                     logger.warning(f"Depth log '{depth_mnemonic}' for well {well_name} is empty or all NaN, and DT log not found. Using 0,0 for default depth range.")
            else:
                logger.error(f"DEPTH log not found for well {well_name} (searched for '{depth_mnemonic}'). Cannot set default depth range. Using 0,0.")

            default_depth_values_for_fallback[well_name] = (default_top_depth_val, default_bottom_depth_val)

        # Create main dialog for method selection
        root = tk.Tk()
        root.title("Set Depth Ranges for Wells")
        root.geometry("600x500")

        # Create a frame
        main_frame = ttk.Frame(root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Method selection
        method_frame = ttk.LabelFrame(main_frame, text="Select Method for Defining Boundaries", padding="10")
        method_frame.pack(fill=tk.X, padx=5, pady=5)

        # Set default method - use "excel" if preloaded Excel data is available
        default_method = "excel" if preloaded_excel_df is not None else "manual"
        method_var = tk.StringVar(value=default_method)

        # Variable to store the submit button reference
        submit_btn_ref = {"btn": None}

        # Define a command to call when radio buttons are clicked
        def on_radio_click():
            method = method_var.get()

            if method == "manual":
                create_manual_ui()
                # Re-enable the Submit button for manual input
                if submit_btn_ref["btn"] is not None:
                    submit_btn_ref["btn"].config(state=tk.NORMAL)
            else:  # excel
                create_excel_ui()
                # Enable Submit button only if Excel data is available
                if submit_btn_ref["btn"] is not None:
                    if excel_data["df"] is not None:
                        submit_btn_ref["btn"].config(state=tk.NORMAL)
                    else:
                        submit_btn_ref["btn"].config(state=tk.DISABLED)

        ttk.Radiobutton(
            method_frame,
            text="Manual Input",
            variable=method_var,
            value="manual",
            command=on_radio_click
        ).pack(anchor=tk.W, pady=2)

        ttk.Radiobutton(
            method_frame,
            text="Import from Excel File",
            variable=method_var,
            value="excel",
            command=on_radio_click
        ).pack(anchor=tk.W, pady=2)

        # Content frame for the selected method
        content_frame = ttk.Frame(main_frame, padding="10")
        content_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(5, 0))

        # Button frame at the bottom
        button_frame = ttk.Frame(main_frame, padding="10")
        button_frame.pack(fill=tk.X, padx=5, pady=(0, 5))

        # Variables to store entries for manual method
        manual_entries = []

        # Variable to store Excel data
        excel_data = {"df": preloaded_excel_df}

        # Output variable
        depth_ranges_output = {}

        # Function to create manual input UI
        def create_manual_ui():
            # Clear the content frame
            for widget in content_frame.winfo_children():
                widget.destroy()

            # Add instructions for manual input
            instructions_frame = ttk.LabelFrame(content_frame, text="Manual Depth Input Instructions", padding="10")
            instructions_frame.pack(fill=tk.X, pady=(0, 10))

            instructions_text = (
                "Enter depth values directly in the fields below:\n"
                "• Top Depth: Starting depth for analysis (typically shallower/smaller value)\n"
                "• Bottom Depth: Ending depth for analysis (typically deeper/larger value)\n"
                "• Values should be in the same units as your LAS files (usually meters or feet)\n"
                "• Top depth must be less than bottom depth for each well"
            )
            ttk.Label(instructions_frame, text=instructions_text, justify=tk.LEFT, wraplength=550).pack(anchor=tk.W)

            # Create scrollable frame for many wells
            canvas = tk.Canvas(content_frame)
            scrollbar = ttk.Scrollbar(content_frame, orient="vertical", command=canvas.yview)
            scrollable_frame = ttk.Frame(canvas)

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            # Create and set column headers with units
            ttk.Label(scrollable_frame, text="Well Name", font=("", 9, "bold")).grid(row=0, column=0, padx=5, pady=5)
            ttk.Label(scrollable_frame, text="Top Depth (m/ft)", font=("", 9, "bold")).grid(row=0, column=1, padx=5, pady=5)
            ttk.Label(scrollable_frame, text="Bottom Depth (m/ft)", font=("", 9, "bold")).grid(row=0, column=2, padx=5, pady=5)
            ttk.Label(scrollable_frame, text="Depth Range", font=("", 9, "bold")).grid(row=0, column=3, padx=5, pady=5)

            # Clear previous entries
            manual_entries.clear()

            # Create entries for each well
            for i, las in enumerate(las_files, start=1):
                well_name = las.well.WELL.value
                default_top, default_bottom = default_depth_values_for_fallback[well_name]

                ttk.Label(scrollable_frame, text=well_name).grid(row=i, column=0, padx=5, pady=2, sticky=tk.W)

                top_entry = ttk.Entry(scrollable_frame, width=12)
                top_entry.insert(0, f"{default_top:.2f}")
                top_entry.grid(row=i, column=1, padx=5, pady=2)

                bottom_entry = ttk.Entry(scrollable_frame, width=12)
                bottom_entry.insert(0, f"{default_bottom:.2f}")
                bottom_entry.grid(row=i, column=2, padx=5, pady=2)

                # Add depth range display
                range_label = ttk.Label(scrollable_frame, text=f"{default_bottom - default_top:.2f}",
                                      foreground="gray", font=("", 8))
                range_label.grid(row=i, column=3, padx=5, pady=2)

                manual_entries.append((well_name, top_entry, bottom_entry, range_label))

        # Function to create Excel input UI
        def create_excel_ui():
            # Clear the content frame
            for widget in content_frame.winfo_children():
                widget.destroy()

            # Add instructions for Excel import
            instructions_frame = ttk.LabelFrame(content_frame, text="Excel Import Instructions", padding="10")
            instructions_frame.pack(fill=tk.X, pady=(0, 10))

            instructions_text = (
                "Import depth boundaries from geological markers in an Excel file:\n"
                "• Excel file must contain columns: 'Well', 'Surface', 'MD' (measured depth)\n"
                "• 'Well' column should match your LAS file well names\n"
                "• 'Surface' column contains geological marker names (e.g., 'Top_Formation', 'Base_Formation')\n"
                "• 'MD' column contains measured depths for each marker\n"
                "• After loading, you'll select top and bottom markers for each well"
            )
            ttk.Label(instructions_frame, text=instructions_text, justify=tk.LEFT, wraplength=550).pack(anchor=tk.W)

            # Create Excel file selection frame
            excel_frame = ttk.LabelFrame(content_frame, text="Excel File Selection", padding="10")
            excel_frame.pack(fill=tk.X, pady=5)

            # File path display
            file_path_var = tk.StringVar()
            if excel_data["df"] is not None:
                file_path_var.set("✓ Pre-loaded Excel data available")
                file_label_color = "green"
            else:
                file_path_var.set("⚠ No file selected")
                file_label_color = "red"

            ttk.Label(excel_frame, text="Status:").pack(anchor=tk.W)
            file_label = ttk.Label(excel_frame, textvariable=file_path_var, foreground=file_label_color)
            file_label.pack(anchor=tk.W, pady=(0, 5))

            def browse_excel_file():
                df = data_io.load_boundaries_from_excel("Select Excel File with Depth Boundaries")
                if df is not None:
                    excel_data["df"] = df
                    file_path_var.set("✓ Excel file loaded successfully")
                    file_label.config(foreground="green")
                    # Enable the Submit button
                    if submit_btn_ref["btn"] is not None:
                        submit_btn_ref["btn"].config(state=tk.NORMAL)
                else:
                    file_path_var.set("⚠ Failed to load file")
                    file_label.config(foreground="red")

            # Show browse button
            if excel_data["df"] is None:
                browse_button = ttk.Button(excel_frame, text="Browse Excel File", command=browse_excel_file)
                browse_button.pack(anchor=tk.W, pady=(5, 0))

                # Add fallback option
                fallback_frame = ttk.Frame(excel_frame)
                fallback_frame.pack(fill=tk.X, pady=(10, 0))

                ttk.Label(fallback_frame, text="Or:", font=("", 9, "italic")).pack(anchor=tk.W)
                fallback_button = ttk.Button(fallback_frame, text="Switch to Manual Input",
                                           command=lambda: [method_var.set("manual"), on_radio_click()])
                fallback_button.pack(anchor=tk.W, pady=(2, 0))
            else:
                # Enable the Submit button for pre-loaded data
                if submit_btn_ref["btn"] is not None:
                    submit_btn_ref["btn"].config(state=tk.NORMAL)

                # Show option to load different file
                change_button = ttk.Button(excel_frame, text="Load Different Excel File", command=browse_excel_file)
                change_button.pack(anchor=tk.W, pady=(5, 0))

        # Initialize UI based on default method
        if default_method == "manual":
            create_manual_ui()
        else:
            create_excel_ui()

        # Force radio button to trigger UI update to ensure synchronization
        root.after(100, on_radio_click)  # Delay to ensure UI is fully initialized

        # Validation helper function
        def validate_manual_entries():
            """
            Validate all manual input entries with comprehensive checks.
            Returns (is_valid, error_message)
            """
            validation_errors = []

            for entry_data in manual_entries:
                if len(entry_data) == 4:  # New format with range label
                    well_name, top_entry, bottom_entry, range_label = entry_data
                else:  # Old format compatibility
                    well_name, top_entry, bottom_entry = entry_data[:3]

                top_str = top_entry.get().strip()
                bottom_str = bottom_entry.get().strip()

                # Check for empty fields
                if not top_str or not bottom_str:
                    validation_errors.append(f"Well '{well_name}': Both top and bottom depths are required")
                    continue

                try:
                    top_depth = float(top_str)
                    bottom_depth = float(bottom_str)
                except ValueError:
                    validation_errors.append(f"Well '{well_name}': Depth values must be numeric")
                    continue

                # Check that top < bottom
                if top_depth >= bottom_depth:
                    validation_errors.append(f"Well '{well_name}': Top depth ({top_depth:.2f}) must be less than bottom depth ({bottom_depth:.2f})")
                    continue

                # Check for reasonable values (non-negative, not too extreme)
                if top_depth < 0 or bottom_depth < 0:
                    validation_errors.append(f"Well '{well_name}': Depth values should be non-negative")
                    continue

                if bottom_depth - top_depth < 0.1:
                    validation_errors.append(f"Well '{well_name}': Depth range too small (minimum 0.1 units)")
                    continue

                if bottom_depth - top_depth > 50000:  # Reasonable maximum range
                    validation_errors.append(f"Well '{well_name}': Depth range seems too large ({bottom_depth - top_depth:.2f} units)")
                    continue

            if validation_errors:
                error_message = "Validation errors found:\n\n" + "\n".join(validation_errors)
                return False, error_message

            return True, ""

        # Submit function
        def submit():
            method = method_var.get()

            if method == "manual":
                # Validate manual entries
                is_valid, error_message = validate_manual_entries()
                if not is_valid:
                    messagebox.showerror("Input Validation Error", error_message)
                    return

                # Process manual entries (validation passed)
                for entry_data in manual_entries:
                    if len(entry_data) == 4:  # New format with range label
                        well_name, top_entry, bottom_entry, range_label = entry_data
                    else:  # Old format compatibility
                        well_name, top_entry, bottom_entry = entry_data[:3]

                    top_depth = float(top_entry.get().strip())
                    bottom_depth = float(bottom_entry.get().strip())
                    depth_ranges_output[well_name] = (top_depth, bottom_depth)

                root.destroy()

            else:  # excel
                if excel_data["df"] is None:
                    # Enhanced fallback logic
                    result = messagebox.askyesno(
                        "No Excel Data",
                        "No Excel file has been loaded.\n\n"
                        "Would you like to switch to Manual Input mode instead?\n"
                        "(Click 'No' to stay in Excel mode and load a file)"
                    )
                    if result:  # User wants to switch to manual
                        method_var.set("manual")
                        on_radio_click()
                        return
                    else:  # User wants to stay in Excel mode
                        return

                df_excel = excel_data["df"]

                # Validate Excel data before proceeding
                las_well_names = [las.well.WELL.value for las in las_files]
                wells_in_excel = df_excel['Well'].unique() if 'Well' in df_excel.columns else []
                matching_wells = [well for well in las_well_names if well in wells_in_excel]

                if not matching_wells:
                    result = messagebox.askyesno(
                        "No Matching Wells",
                        f"No wells from your LAS files were found in the Excel data.\n\n"
                        f"LAS wells: {', '.join(las_well_names)}\n"
                        f"Excel wells: {', '.join(wells_in_excel)}\n\n"
                        f"Would you like to switch to Manual Input mode instead?"
                    )
                    if result:  # User wants to switch to manual
                        method_var.set("manual")
                        on_radio_click()
                        return
                    else:  # User wants to stay and try different file
                        return

                # Show a dialog to select boundaries for all wells at once
                logger.info("Opening batch selection dialog for all wells...")
                try:
                    all_boundaries = self._select_boundaries_for_all_wells(df_excel, las_well_names)
                except Exception as e:
                    messagebox.showerror("Error", f"Error during boundary selection: {str(e)}")
                    return

                if all_boundaries:
                    # Apply the selected boundaries
                    for well_name, boundaries in all_boundaries.items():
                        depth_ranges_output[well_name] = boundaries
                        logger.info(f"Boundaries set for {well_name} from Excel batch selection: {boundaries}")

                    # Close the main dialog
                    root.destroy()
                else:
                    # User cancelled the batch selection dialog
                    # Offer fallback to manual input
                    result = messagebox.askyesno(
                        "Selection Cancelled",
                        "Boundary selection was cancelled.\n\n"
                        "Would you like to switch to Manual Input mode to enter depths directly?"
                    )
                    if result:  # User wants to switch to manual
                        method_var.set("manual")
                        on_radio_click()
                        return

        # Create Submit button in the dedicated button frame
        submit_button = ttk.Button(button_frame, text="Submit", command=submit)
        submit_button.pack(pady=5)
        submit_btn_ref["btn"] = submit_button

        # Set initial button state
        if default_method == "excel" and excel_data["df"] is None:
            submit_button.config(state=tk.DISABLED)
        else:
            submit_button.config(state=tk.NORMAL)

        # Make the dialog modal and wait for it to close
        try:
            root.transient()  # Make it a transient window
            root.wait_visibility()  # Wait for the window to be visible
            root.focus_set()  # Give it focus

            # Try to grab focus, but handle the case where another dialog has grab
            try:
                root.grab_set()   # Make it modal
            except tk.TclError as e:
                if "grab failed" in str(e):
                    # Another dialog has grab, just make it topmost instead
                    root.lift()
                    root.attributes('-topmost', True)
                    root.after(100, lambda: root.attributes('-topmost', False))
                else:
                    raise

            # Wait for the dialog to be destroyed instead of using mainloop
            root.wait_window()

        except Exception as e:
            logger.error(f"Error in modal dialog setup: {str(e)}")
            # Fallback: just wait for the window without modal behavior
            root.wait_window()

        return depth_ranges_output


# Convenience function for backward compatibility
def get_enhanced_depth_ranges(las_files, log_keywords, preloaded_excel_df=None):
    """
    Convenience function to get depth ranges using the enhanced dialog system.

    Args:
        las_files: List of LAS file objects
        log_keywords: Dictionary mapping generic names to possible mnemonics
        preloaded_excel_df: Optional pre-loaded Excel DataFrame with depth ranges

    Returns:
        Dictionary mapping well names to (top_depth, bottom_depth) tuples
    """
    dialog_system = EnhancedDepthDialog()
    return dialog_system.get_depth_ranges(las_files, log_keywords, preloaded_excel_df)
